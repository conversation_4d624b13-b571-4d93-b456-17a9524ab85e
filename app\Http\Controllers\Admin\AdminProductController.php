<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\WarehouseStock;
use App\Models\Distribution;
use App\Models\StoreStock;
use App\Services\ExcelExportService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class AdminProductController extends Controller
{
    /**
     * Display a listing of products
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        $warehouseId = $user->getWarehouseId();

        // Scope products to those with warehouse stock in this admin's warehouse
        $query = Product::whereHas('warehouseStock', function ($q) use ($warehouseId) {
            $q->where('warehouse_id', $warehouseId);
        })->with([
            'warehouseStock' => function ($q) use ($warehouseId) {
                $q->where('warehouse_id', $warehouseId);
            },
            'storeStock',
            'distributions' => function ($q) use ($warehouseId) {
                $q->where('warehouse_id', $warehouseId);
            }
        ]);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where('name', 'like', "%{$search}%");
        }

        // Sort by
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');

        if (in_array($sortBy, ['name', 'created_at', 'updated_at'])) {
            $query->orderBy($sortBy, $sortOrder);
        } else {
            $query->orderBy('created_at', 'desc');
        }

        $products = $query->paginate(10);

        // Get enhanced statistics scoped to this warehouse
        $totalWarehouseStock = WarehouseStock::where('warehouse_id', $warehouseId)->sum('quantity');
        $totalPendingDistributions = 0; // With auto-acceptance, no pending distributions exist

        $stats = [
            'total' => Product::whereHas('warehouseStock', function ($q) use ($warehouseId) {
                $q->where('warehouse_id', $warehouseId);
            })->count(),
            'warehouse_stock' => $totalWarehouseStock,
            'pending_distributions' => $totalPendingDistributions,
            'available_stock' => $totalWarehouseStock, // All warehouse stock is available
            'distributions' => Distribution::where('warehouse_id', $warehouseId)->count(),
            'store_stock' => StoreStock::whereHas('store.distributions', function ($q) use ($warehouseId) {
                $q->where('warehouse_id', $warehouseId);
            })->sum('quantity'), // Store stock for stores that received from this warehouse
        ];

        return view('admin.products.index', compact('products', 'stats'));
    }

    /**
     * Show the form for creating a new product
     */
    public function create(Request $request)
    {
        // For simplified system, we don't use categories
        // Pass empty collection to prevent undefined variable error
        $categories = collect();

        return view('admin.products.create', compact('categories'));
    }

    /**
     * Store a newly created product in storage
     */
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255|unique:products,name',
            'initial_stock' => 'required|integer|min:0',
        ], [
            'name.required' => 'Nama produk wajib diisi',
            'name.string' => 'Nama produk harus berupa teks',
            'name.max' => 'Nama produk maksimal 255 karakter',
            'name.unique' => 'Nama produk sudah digunakan. Silakan gunakan nama yang berbeda',
            'initial_stock.required' => 'Stok awal wajib diisi',
            'initial_stock.integer' => 'Stok awal harus berupa angka bulat',
            'initial_stock.min' => 'Stok awal tidak boleh kurang dari 0',
        ]);

        // Create the product
        $product = Product::create([
            'name' => $validatedData['name']
        ]);

        // Create warehouse stock entry if initial stock > 0
        if ($validatedData['initial_stock'] > 0) {
            \App\Models\WarehouseStock::create([
                'product_id' => $product->id,
                'quantity' => $validatedData['initial_stock'],
                'store_id' => null, // Warehouse stock doesn't belong to specific store
                'date_received' => now()->toDateString(),
            ]);
        }

        return redirect()->route('admin.products.index')
            ->with('success', 'Produk berhasil dibuat dengan stok awal: ' . $validatedData['initial_stock'] . ' unit');
    }

    /**
     * Display the specified product
     */
    public function show(Product $product)
    {
        $user = auth()->user();
        $warehouseId = $user->getWarehouseId();

        $product->load([
            'warehouseStock' => function ($q) use ($warehouseId) {
                $q->where('warehouse_id', $warehouseId);
            },
            'distributions' => function ($q) use ($warehouseId) {
                $q->where('warehouse_id', $warehouseId);
            },
            'storeStock',
            'stockOpname'
        ]);

        $stats = [
            'warehouse_stock' => $product->warehouseStock->sum('quantity'),
            'distributions_count' => $product->distributions()->where('warehouse_id', $warehouseId)->count(),
            'store_stock' => $product->storeStock->sum('quantity'),
            'stock_opname_count' => $product->stockOpname()->count(),
        ];

        return view('admin.products.show', compact('product', 'stats'));
    }

    /**
     * Show the form for editing the specified product
     */
    public function edit(Product $product)
    {
        // For simplified system, we don't use categories
        // Pass empty collection to prevent undefined variable error
        $categories = collect();

        return view('admin.products.edit', compact('product', 'categories'));
    }

    /**
     * Update the specified product in storage
     */
    public function update(Request $request, Product $product)
    {
        $validatedData = $request->validate([
            'name' => ['required', 'string', 'max:255', Rule::unique('products')->ignore($product->id)],
        ], [
            'name.required' => 'Nama produk wajib diisi',
            'name.string' => 'Nama produk harus berupa teks',
            'name.max' => 'Nama produk maksimal 255 karakter',
            'name.unique' => 'Nama produk sudah digunakan. Silakan gunakan nama yang berbeda',
        ]);

        $product->update($validatedData);

        return redirect()->route('admin.products.index')
            ->with('success', 'Produk berhasil diperbarui: ' . $product->name);
    }

    /**
     * Remove the specified product from storage
     * Now allows deletion even with blocking reasons after admin confirmation
     */
    public function destroy(Product $product)
    {
        $productName = $product->name;

        // Get blocking reasons for logging purposes
        $blockingReasons = $product->getDeletionBlockingReasons();

        try {
            // Use database transaction to ensure data integrity
            DB::beginTransaction();

            // Delete related records first to avoid foreign key constraints
            // Note: The database cascade deletes should handle this, but we'll be explicit

            // Delete warehouse stock
            $product->warehouseStock()->delete();

            // Delete store stock
            $product->storeStock()->delete();

            // Delete distributions
            $product->distributions()->delete();

            // Delete stock opname records
            $product->stockOpname()->delete();

            // Finally delete the product
            $product->delete();

            DB::commit();

            // Create success message with details about what was deleted
            $deletedItems = [];
            foreach ($blockingReasons as $reason) {
                switch ($reason['type']) {
                    case 'warehouse_stock':
                        $deletedItems[] = "{$reason['quantity']} unit stok gudang";
                        break;
                    case 'distributions':
                        $deletedItems[] = "{$reason['count']} riwayat distribusi";
                        break;
                    case 'store_stock':
                        $deletedItems[] = "{$reason['quantity']} unit stok toko";
                        break;
                    case 'stock_opname':
                        $deletedItems[] = "{$reason['count']} riwayat stock opname";
                        break;
                }
            }

            $successMessage = 'Produk "' . $productName . '" berhasil dihapus';
            if (!empty($deletedItems)) {
                $successMessage .= ' beserta ' . implode(', ', $deletedItems);
            }
            $successMessage .= '.';

            return redirect()->route('admin.products.index')
                ->with('success', $successMessage);

        } catch (\Exception $e) {
            DB::rollback();

            return redirect()->route('admin.products.index')
                ->with('error', 'Gagal menghapus produk "' . $productName . '". Error: ' . $e->getMessage());
        }
    }

    /**
     * Export products data to Excel
     */
    public function export(Request $request, ExcelExportService $excelService)
    {
        // Get user's timezone
        $userTimezone = auth()->user()->timezone ?? 'Asia/Jakarta';

        // Apply same filters as index method
        $query = Product::query();

        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where('name', 'like', "%{$search}%");
        }

        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');

        if (in_array($sortBy, ['name', 'created_at', 'updated_at'])) {
            $query->orderBy($sortBy, $sortOrder);
        } else {
            $query->orderBy('name', 'asc');
        }

        $products = $query->with(['warehouseStock', 'distributions', 'storeStock'])->get();

        // Prepare worksheets data
        $worksheets = [];

        // Products worksheet
        $productData = [];
        $no = 1;
        foreach ($products as $product) {
            $warehouseStock = $product->getCurrentWarehouseStock();
            $pendingDistributions = $product->getPendingDistributionsQuantity();
            $availableStock = $product->getAvailableStock();
            $totalDistributions = $product->distributions()->count();
            $storeStock = $product->storeStock->sum('quantity');
            $createdAt = $excelService->formatDate($product->created_at, $userTimezone);

            $productData[] = [
                $no++,
                $product->name,
                $warehouseStock,
                $pendingDistributions,
                $availableStock,
                $totalDistributions,
                $storeStock,
                $createdAt
            ];
        }

        $worksheets[] = [
            'name' => 'Data Produk',
            'title' => 'DATA PRODUK - INDAH BERKAH ABADI',
            'headers' => ['No', 'Nama Produk', 'Stok Gudang', 'Pending Distribusi', 'Stok Tersedia', 'Total Distribusi', 'Stok Toko', 'Dibuat Pada'],
            'data' => $productData
        ];

        // Create filename
        $filename = $excelService->createFilename('data_produk');

        return $excelService->generateExcelFile($worksheets, $filename, $userTimezone);
    }
}
