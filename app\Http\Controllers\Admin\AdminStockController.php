<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Distribution;
use App\Models\WarehouseStock;
use App\Models\StoreStock;
use App\Models\StockMovement;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AdminStockController extends Controller
{
    /**
     * Display stock overview and management
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        $warehouseId = $user->getWarehouseId();

        // Scope products to those with warehouse stock in this admin's warehouse
        $query = Product::whereHas('warehouseStock', function ($q) use ($warehouseId) {
            $q->where('warehouse_id', $warehouseId);
        })->with([
            'warehouseStock' => function ($q) use ($warehouseId) {
                $q->where('warehouse_id', $warehouseId);
            },
            'storeStock',
            'distributions' => function ($q) use ($warehouseId) {
                $q->where('warehouse_id', $warehouseId);
            }
        ]);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where('name', 'like', "%{$search}%");
        }

        $products = $query->orderBy('created_at', 'desc')->paginate(10);

        // Get enhanced stock statistics scoped to this warehouse
        $totalProducts = Product::whereHas('warehouseStock', function ($q) use ($warehouseId) {
            $q->where('warehouse_id', $warehouseId);
        })->count();
        $totalWarehouseStock = \App\Models\WarehouseStock::where('warehouse_id', $warehouseId)
            ->whereNull('store_id')->sum('quantity'); // Only this warehouse's stock
        $totalPendingDistributions = 0; // With auto-acceptance, no pending distributions exist
        $totalAvailableStock = $totalWarehouseStock; // All warehouse stock is available since no pending distributions
        // Get store stock for stores that received distributions from this warehouse
        $totalStoreStock = \App\Models\StoreStock::whereHas('store.distributions', function ($q) use ($warehouseId) {
            $q->where('warehouse_id', $warehouseId);
        })->sum('quantity');
        $totalDistributions = \App\Models\Distribution::where('warehouse_id', $warehouseId)->count();

        return view('admin.stock.index', compact(
            'products',
            'totalProducts',
            'totalWarehouseStock',
            'totalPendingDistributions',
            'totalAvailableStock',
            'totalStoreStock',
            'totalDistributions'
        ));
    }

    /**
     * Show stock movements for a specific product
     */
    public function movements(Product $product)
    {
        $user = auth()->user();
        $warehouseId = $user->getWarehouseId();

        // Get stock movements scoped to this warehouse
        $movements = StockMovement::where('product_id', $product->id)
            ->where('warehouse_id', $warehouseId)
            ->with(['creator'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('admin.stock.movements', compact('product', 'movements'));
    }

    /**
     * Show form for stock adjustment
     */
    public function adjustForm(Product $product)
    {
        return view('admin.stock.adjust', compact('product'));
    }

    /**
     * Process stock adjustment with enhanced validation and audit logging
     */
    public function adjust(Request $request, Product $product)
    {

        // Enhanced validation
        $validatedData = $request->validate([
            'adjustment_type' => 'required|in:increase,decrease,set',
            'quantity' => 'required|integer|min:1',
            'notes' => 'nullable|string|max:500'
        ], [
            'adjustment_type.required' => 'Silakan pilih jenis penyesuaian stok (Tambah, Kurangi, atau Set Stok Baru)',
            'adjustment_type.in' => 'Jenis penyesuaian yang dipilih tidak valid. Pilih salah satu: Tambah Stok, Kurangi Stok, atau Set Stok Baru',
            'quantity.required' => 'Jumlah stok wajib diisi. Masukkan angka yang valid',
            'quantity.integer' => 'Jumlah stok harus berupa angka bulat (tidak boleh desimal)',
            'quantity.min' => 'Jumlah stok harus lebih dari 0. Masukkan angka positif',
            'notes.max' => 'Catatan terlalu panjang. Maksimal 500 karakter diperbolehkan',
        ]);

        $quantity = $validatedData['quantity'];
        $notes = $validatedData['notes'] ?? '';
        $adjustmentType = $validatedData['adjustment_type'];

        // Additional validation for decrease and set operations
        if ($adjustmentType === 'decrease') {
            if (!$product->canReduceStock($quantity)) {
                $stockSummary = $product->getStockSummary();

                $errorMessage = "Pengurangan stok gagal! ";
                $errorMessage .= "Anda mencoba mengurangi {$quantity} unit, tetapi hanya tersedia {$stockSummary['available_stock']} unit untuk pengurangan. ";
                $errorMessage .= "Detail: Stok gudang saat ini {$stockSummary['warehouse_stock']} unit, namun {$stockSummary['pending_distributions']} unit sudah dialokasikan untuk distribusi yang belum dikonfirmasi. ";
                $errorMessage .= "Silakan konfirmasi distribusi terlebih dahulu atau kurangi jumlah pengurangan.";

                return redirect()->back()
                    ->withInput()
                    ->withErrors([
                        'quantity' => $errorMessage
                    ]);
            }
        } elseif ($adjustmentType === 'set') {
            if (!$product->canSetStock($quantity)) {
                $stockSummary = $product->getStockSummary();

                $errorMessage = "Pengaturan stok gagal! ";
                $errorMessage .= "Anda mencoba mengatur stok menjadi {$quantity} unit, tetapi minimal harus {$stockSummary['pending_distributions']} unit. ";
                $errorMessage .= "Hal ini karena {$stockSummary['pending_distributions']} unit sudah dialokasikan untuk distribusi yang belum dikonfirmasi. ";
                $errorMessage .= "Silakan konfirmasi distribusi terlebih dahulu atau atur stok dengan jumlah yang lebih besar.";

                return redirect()->back()
                    ->withInput()
                    ->withErrors([
                        'quantity' => $errorMessage
                    ]);
            }
        }

        // Process stock adjustment with audit logging
        DB::transaction(function () use ($product, $adjustmentType, $quantity, $notes) {
            // Find or create warehouse stock entry (central warehouse only)
            $warehouseStock = WarehouseStock::where('product_id', $product->id)
                ->whereNull('store_id')
                ->first();

            if (!$warehouseStock) {
                // Create new warehouse stock entry if it doesn't exist
                $warehouseStock = WarehouseStock::create([
                    'product_id' => $product->id,
                    'quantity' => 0,
                    'store_id' => null, // Central warehouse stock doesn't belong to specific store
                    'date_received' => now()->toDateString(),
                ]);
            }

            $previousStock = $warehouseStock->quantity;

            // Calculate new stock
            switch ($adjustmentType) {
                case 'increase':
                    $newStock = $previousStock + $quantity;
                    $movementQuantity = $quantity;
                    $movementType = 'in';
                    break;
                case 'decrease':
                    $newStock = $previousStock - $quantity;
                    $movementQuantity = $quantity; // Positive quantity for movement record
                    $movementType = 'out';
                    break;
                case 'set':
                    $newStock = $quantity;
                    $movementQuantity = abs($quantity - $previousStock);
                    $movementType = $quantity > $previousStock ? 'in' : 'out';
                    break;
            }

            // Update warehouse stock
            $warehouseStock->update(['quantity' => $newStock]);

            // Create stock movement record for audit
            StockMovement::create([
                'product_id' => $product->id,
                'type' => $movementType,
                'source' => 'adjustment',
                'quantity' => $movementQuantity,
                'previous_stock' => $previousStock,
                'new_stock' => $newStock,
                'reference_type' => null,
                'reference_id' => null,
                'notes' => $notes ?: "Penyesuaian stok: " . match($adjustmentType) {
                    'increase' => "Penambahan {$quantity} unit",
                    'decrease' => "Pengurangan {$quantity} unit",
                    'set' => "Set stok menjadi {$quantity} unit (dari {$previousStock} unit)"
                },
                'created_by' => auth()->id(),
            ]);
        });

        $actionText = match($adjustmentType) {
            'increase' => "ditambahkan sebanyak {$quantity} unit",
            'decrease' => "dikurangi sebanyak {$quantity} unit",
            'set' => "diatur menjadi {$quantity} unit"
        };

        return redirect()->route('admin.stock.index')
            ->with('success', "Stok produk {$product->name} berhasil {$actionText}");
    }
}
