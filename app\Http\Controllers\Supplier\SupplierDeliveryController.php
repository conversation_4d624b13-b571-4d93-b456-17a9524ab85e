<?php

namespace App\Http\Controllers\Supplier;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\SupplierDelivery;
use App\Models\Supplier;
use App\Models\Product;
use App\Models\User;
use Carbon\Carbon;
use App\Traits\SupplierHelper;

class SupplierDeliveryController extends Controller
{
    use SupplierHelper;
    /**
     * Display a listing of supplier deliveries.
     */
    public function index(Request $request)
    {
        // Get current supplier using the helper trait
        $supplier = $this->getCurrentSupplier();

        $query = SupplierDelivery::with(['supplier', 'product', 'receivedBy', 'warehouse'])
            ->where('supplier_id', $supplier->id); // Scope to current supplier

        // Month filter - show all data by default, filter only if month is specified
        $filterMonth = $request->get('month');
        $startDate = null;
        $endDate = null;

        if ($filterMonth && $filterMonth !== 'all') {
            // Parse the filter month
            $startDate = Carbon::createFromFormat('Y-m', $filterMonth)->startOfMonth();
            $endDate = Carbon::createFromFormat('Y-m', $filterMonth)->endOfMonth();

            // Apply date filter only when month is selected
            $query->whereBetween('delivery_date', [$startDate, $endDate]);
        }
        
        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->whereHas('supplier', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            })->orWhereHas('product', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            });
        }
        
        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }
        
        // Sort by
        $sortBy = $request->get('sort_by', 'delivery_date');
        $sortOrder = $request->get('sort_order', 'desc');
        
        if (in_array($sortBy, ['delivery_date', 'received_date', 'quantity', 'status'])) {
            $query->orderBy($sortBy, $sortOrder);
        } else {
            $query->orderBy('delivery_date', 'desc');
        }
        
        $deliveries = $query->paginate(10);
        
        // Get statistics - apply same filtering logic as main query
        $statsQuery = SupplierDelivery::where('supplier_id', $supplier->id); // Scope to current supplier
        if ($filterMonth && $filterMonth !== 'all') {
            $statsQuery->whereBetween('delivery_date', [$startDate, $endDate]);
        }

        $stats = [
            'total' => (clone $statsQuery)->count(),
            'pending' => (clone $statsQuery)->where('status', 'pending')->count(),
            'received' => (clone $statsQuery)->where('status', 'received')->count(),
            'partial' => (clone $statsQuery)->where('status', 'partial')->count(),
        ];

        // Get available months for filter dropdown
        $availableMonths = $this->getAvailableMonths();

        return view('supplier.deliveries.index', compact('deliveries', 'stats', 'filterMonth', 'availableMonths'));
    }
    
    /**
     * Show the form for creating a new delivery.
     */
    public function create()
    {
        // Get current supplier using the helper trait
        $supplier = $this->getCurrentSupplier();

        // Get warehouse admin users (admin role) to deliver TO
        $warehouseAdmins = User::where('role', 'admin')->orderBy('name')->get();

        // Get all products (shared catalog across all suppliers)
        // Products should be shared across all accounts - only quantities/distributions are isolated
        $products = Product::orderBy('name')->get();

        return view('supplier.deliveries.create', compact('warehouseAdmins', 'products'));
    }
    
    /**
     * Store a newly created delivery in storage.
     */
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'warehouse_admin_id' => 'required|exists:users,id',
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:1',
            'unit_price' => 'nullable|numeric|min:0',
            'delivery_date' => 'required|date',
            'notes' => 'nullable|string|max:1000',
        ], [
            'warehouse_admin_id.required' => 'Admin gudang wajib dipilih',
            'warehouse_admin_id.exists' => 'Admin gudang tidak valid',
            'product_id.required' => 'Produk wajib dipilih',
            'product_id.exists' => 'Produk tidak valid',
            'quantity.required' => 'Jumlah wajib diisi',
            'quantity.integer' => 'Jumlah harus berupa angka bulat',
            'quantity.min' => 'Jumlah minimal 1',
            'unit_price.numeric' => 'Harga satuan harus berupa angka',
            'unit_price.min' => 'Harga satuan tidak boleh negatif',
            'delivery_date.required' => 'Tanggal pengiriman wajib diisi',
            'delivery_date.date' => 'Format tanggal tidak valid',
            'notes.max' => 'Catatan maksimal 1000 karakter',
        ]);

        // Calculate total price if unit price is provided
        if ($validatedData['unit_price']) {
            $validatedData['total_price'] = $validatedData['unit_price'] * $validatedData['quantity'];
        }

        // Get the current supplier using the helper trait
        $supplier = $this->getCurrentSupplier();

        // Get the warehouse admin user to determine warehouse_id
        $warehouseAdmin = User::findOrFail($validatedData['warehouse_admin_id']);

        // Validate that the selected user is actually an admin with a warehouse
        if (!$warehouseAdmin->isAdmin() || !$warehouseAdmin->warehouse_id) {
            return back()->withErrors(['warehouse_admin_id' => 'Admin gudang yang dipilih tidak valid atau tidak memiliki gudang yang ditugaskan.']);
        }

        $validatedData['supplier_id'] = $supplier->id;
        $validatedData['warehouse_id'] = $warehouseAdmin->warehouse_id; // Set the correct warehouse_id
        unset($validatedData['warehouse_admin_id']); // Remove this as it's not in the SupplierDelivery model

        // Auto-accept the delivery in simplified system
        $validatedData['status'] = 'received';
        $validatedData['received_quantity'] = $validatedData['quantity'];
        $validatedData['received_date'] = $validatedData['delivery_date'];
        $validatedData['received_by'] = auth()->id();

        \DB::transaction(function () use ($validatedData) {
            $delivery = SupplierDelivery::create($validatedData);

            // Add to warehouse stock immediately with correct warehouse_id
            \App\Models\WarehouseStock::create([
                'product_id' => $validatedData['product_id'],
                'quantity' => $validatedData['quantity'],
                'store_id' => null, // Central warehouse
                'warehouse_id' => $validatedData['warehouse_id'], // Assign to correct warehouse
                'date_received' => $validatedData['delivery_date'],
            ]);

            // Record stock movement with correct warehouse scoping
            $previousStock = \App\Models\WarehouseStock::where('product_id', $validatedData['product_id'])
                ->whereNull('store_id')
                ->where('warehouse_id', $validatedData['warehouse_id'])
                ->sum('quantity') - $validatedData['quantity'];

            \App\Models\StockMovement::create([
                'product_id' => $validatedData['product_id'],
                'type' => 'in',
                'source' => 'supplier',
                'quantity' => $validatedData['quantity'],
                'previous_stock' => $previousStock,
                'new_stock' => $previousStock + $validatedData['quantity'],
                'reference_type' => 'SupplierDelivery',
                'reference_id' => $delivery->id,
                'warehouse_id' => $validatedData['warehouse_id'], // Track warehouse for stock movement
                'notes' => 'Auto-penerimaan dari supplier: ' . $delivery->supplier->name,
                'created_by' => auth()->id(),
            ]);
        });

        return redirect()->route('supplier.deliveries.index')
            ->with('success', 'Pengiriman berhasil ditambahkan dan diterima secara otomatis di gudang');
    }

    /**
     * Create a new product via AJAX from supplier delivery form.
     */
    public function createProduct(Request $request)
    {
        // Get current supplier using the helper trait
        $supplier = $this->getCurrentSupplier();

        $validatedData = $request->validate([
            'name' => 'required|string|max:255|unique:products,name',
        ], [
            'name.required' => 'Nama produk wajib diisi',
            'name.string' => 'Nama produk harus berupa teks',
            'name.max' => 'Nama produk maksimal 255 karakter',
            'name.unique' => 'Nama produk sudah digunakan. Silakan gunakan nama yang berbeda',
        ]);

        // Create the product and assign it to this supplier
        $product = Product::create([
            'name' => $validatedData['name'],
            'supplier_id' => $supplier->id
        ]);

        // Return JSON response for AJAX
        return response()->json([
            'success' => true,
            'product' => [
                'id' => $product->id,
                'name' => $product->name
            ],
            'message' => 'Produk berhasil dibuat: ' . $product->name
        ]);
    }

    /**
     * Display the specified delivery.
     */
    public function show(SupplierDelivery $delivery)
    {
        $delivery->load(['supplier', 'product', 'receivedBy', 'warehouse']);

        return view('supplier.deliveries.show', compact('delivery'));
    }
    

    /**
     * Remove the specified delivery from storage.
     */
    public function destroy(SupplierDelivery $delivery)
    {
        // Only allow deletion if delivery is still pending
        if ($delivery->status !== 'pending') {
            return redirect()->route('supplier.deliveries.index')
                ->with('error', 'Pengiriman yang sudah diterima tidak dapat dihapus');
        }
        
        $delivery->delete();
        
        return redirect()->route('supplier.deliveries.index')
            ->with('success', 'Pengiriman berhasil dihapus');
    }

    /**
     * Get available months for filter dropdown.
     */
    private function getAvailableMonths()
    {
        $months = [];

        // Add "All Time" option
        $months[] = [
            'value' => 'all',
            'label' => 'Semua Waktu'
        ];

        // Get current supplier using the helper trait
        $supplier = $this->getCurrentSupplier();

        // Get months from existing supplier deliveries for this supplier only
        $deliveryMonths = SupplierDelivery::where('supplier_id', $supplier->id)
            ->selectRaw('DATE_FORMAT(delivery_date, "%Y-%m") as month')
            ->distinct()
            ->orderBy('month', 'desc')
            ->pluck('month');

        foreach ($deliveryMonths as $month) {
            $date = Carbon::createFromFormat('Y-m', $month);
            $months[] = [
                'value' => $month,
                'label' => $date->format('F Y')
            ];
        }

        return $months;
    }
}
