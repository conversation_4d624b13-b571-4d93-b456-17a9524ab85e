<?php

namespace App\Http\Controllers\Supplier;

use App\Http\Controllers\Controller;
use App\Services\TimezoneService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Password;

class SupplierProfileController extends Controller
{
    /**
     * Display the supplier profile.
     */
    public function show()
    {
        $user = auth()->user();
        $supplier = $user->supplier; // Get the supplier company information

        return view('supplier.profile.show', compact('user', 'supplier'));
    }
    
    /**
     * Show the form for editing the supplier profile.
     */
    public function edit()
    {
        $user = auth()->user();
        $supplier = $user->supplier; // Get the supplier company information
        $timezones = TimezoneService::getAvailableTimezones();

        return view('supplier.profile.edit', compact('user', 'supplier', 'timezones'));
    }
    
    /**
     * Update the supplier profile.
     */
    public function update(Request $request)
    {
        $user = auth()->user();
        $supplier = $user->supplier;

        $validatedData = $request->validate([
            // User account fields
            'name' => 'required|string|max:255',
            'email' => ['required', 'email', Rule::unique('users')->ignore($user->id)],
            'timezone' => 'required|in:Asia/Jakarta,Asia/Makassar,Asia/Jayapura',

            // Supplier company fields
            'supplier_name' => 'required|string|max:255',
            'contact_person' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:20',
            'supplier_email' => 'nullable|email|max:255',
            'address' => 'nullable|string|max:1000',
        ], [
            // User validation messages
            'name.required' => 'Nama akun wajib diisi',
            'name.string' => 'Nama akun harus berupa teks',
            'name.max' => 'Nama akun maksimal 255 karakter',
            'email.required' => 'Email akun wajib diisi',
            'email.email' => 'Format email akun tidak valid',
            'email.unique' => 'Email akun sudah digunakan',
            'timezone.required' => 'Zona waktu wajib dipilih',
            'timezone.in' => 'Zona waktu tidak valid',

            // Supplier validation messages
            'supplier_name.required' => 'Nama perusahaan wajib diisi',
            'supplier_name.string' => 'Nama perusahaan harus berupa teks',
            'supplier_name.max' => 'Nama perusahaan maksimal 255 karakter',
            'contact_person.string' => 'Nama kontak harus berupa teks',
            'contact_person.max' => 'Nama kontak maksimal 255 karakter',
            'phone.string' => 'Nomor telepon harus berupa teks',
            'phone.max' => 'Nomor telepon maksimal 20 karakter',
            'supplier_email.email' => 'Format email perusahaan tidak valid',
            'supplier_email.max' => 'Email perusahaan maksimal 255 karakter',
            'address.string' => 'Alamat harus berupa teks',
            'address.max' => 'Alamat maksimal 1000 karakter',
        ]);

        \DB::transaction(function () use ($user, $supplier, $validatedData) {
            // Update user account information
            $user->update([
                'name' => $validatedData['name'],
                'email' => $validatedData['email'],
                'timezone' => $validatedData['timezone'],
            ]);

            // Update supplier company information
            if ($supplier) {
                $supplier->update([
                    'name' => $validatedData['supplier_name'],
                    'contact_person' => $validatedData['contact_person'],
                    'phone' => $validatedData['phone'],
                    'email' => $validatedData['supplier_email'],
                    'address' => $validatedData['address'],
                ]);
            }
        });

        return redirect()->route('supplier.profile.show')
            ->with('success', 'Profil akun dan informasi perusahaan berhasil diperbarui');
    }

    /**
     * Show password change form
     */
    public function showPasswordForm()
    {
        return view('supplier.profile.password');
    }

    /**
     * Update supplier password (simplified - no current password required)
     */
    public function updatePassword(Request $request)
    {
        $request->validate([
            'password' => ['required', 'confirmed', Password::defaults()],
        ], [
            'password.required' => 'Kata sandi baru wajib diisi',
            'password.confirmed' => 'Konfirmasi kata sandi tidak cocok',
        ]);

        auth()->user()->update([
            'password' => Hash::make($request->password),
        ]);

        return redirect()->route('supplier.profile.show')
            ->with('success', 'Kata sandi berhasil diperbarui');
    }
}
