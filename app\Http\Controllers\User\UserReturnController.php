<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\ReturnModel;
use App\Models\Product;
use App\Models\StoreStock;
use App\Models\Supplier;
use Carbon\Carbon;

class UserReturnController extends Controller
{
    /**
     * Display a listing of returns for the user's store.
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        
        // Only allow store users
        if (!$user->isUser() || !$user->store_id) {
            abort(403, 'Akses ditolak');
        }
        
        $query = ReturnModel::with(['product', 'supplier', 'requestedBy', 'approvedBy'])
            ->where('store_id', $user->store_id);
        
        // Month filter - show all data by default, filter only if month is specified
        $filterMonth = $request->get('month');
        $startDate = null;
        $endDate = null;

        if ($filterMonth && $filterMonth !== 'all') {
            // Parse the filter month
            $startDate = Carbon::createFromFormat('Y-m', $filterMonth)->startOfMonth();
            $endDate = Carbon::createFromFormat('Y-m', $filterMonth)->endOfMonth();

            // Apply date filter only when month is selected
            $query->whereBetween('return_date', [$startDate, $endDate]);
        }
        
        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->whereHas('product', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            });
        }
        
        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }
        
        // Sort by
        $sortBy = $request->get('sort_by', 'return_date');
        $sortOrder = $request->get('sort_order', 'desc');
        
        if (in_array($sortBy, ['return_date', 'approved_date', 'completed_date', 'quantity', 'status'])) {
            $query->orderBy($sortBy, $sortOrder);
        } else {
            $query->orderBy('return_date', 'desc');
        }
        
        $returns = $query->paginate(10);
        
        // Get statistics - apply same filtering logic as main query
        $statsQuery = ReturnModel::where('store_id', $user->store_id);
        if ($filterMonth && $filterMonth !== 'all') {
            $statsQuery->whereBetween('return_date', [$startDate, $endDate]);
        }

        $stats = [
            'total' => (clone $statsQuery)->count(),
            'requested' => (clone $statsQuery)->where('status', 'requested')->count(),
            'approved' => (clone $statsQuery)->where('status', 'approved')->count(),
            'completed' => (clone $statsQuery)->where('status', 'completed')->count(),
        ];

        // Get available months for filter dropdown
        $availableMonths = $this->getAvailableMonths($user->store_id);

        return view('user.returns.index', compact('returns', 'stats', 'filterMonth', 'availableMonths'));
    }
    
    /**
     * Show the form for creating a new return.
     */
    public function create()
    {
        $user = auth()->user();
        
        // Only allow store users
        if (!$user->isUser() || !$user->store_id) {
            abort(403, 'Akses ditolak');
        }
        
        // Get products that have stock in this store
        $products = Product::whereHas('storeStock', function ($query) use ($user) {
            $query->where('store_id', $user->store_id)
                  ->where('quantity', '>', 0);
        })->orderBy('name')->get();
        
        // Get active suppliers
        $suppliers = Supplier::active()->orderBy('name')->get();
        
        return view('user.returns.create', compact('products', 'suppliers'));
    }
    
    /**
     * Store a newly created return in storage.
     */
    public function store(Request $request)
    {
        $user = auth()->user();
        
        // Only allow store users
        if (!$user->isUser() || !$user->store_id) {
            abort(403, 'Akses ditolak');
        }
        
        $validatedData = $request->validate([
            'product_id' => 'required|exists:products,id',
            'supplier_id' => 'nullable|exists:suppliers,id',
            'quantity' => 'required|integer|min:1',
            'reason' => 'required|in:damaged,expired,defective,overstock,other',
            'description' => 'required|string|max:1000',
            'return_date' => 'required|date|before_or_equal:today',
        ], [
            'product_id.required' => 'Produk wajib dipilih',
            'product_id.exists' => 'Produk tidak valid',
            'supplier_id.exists' => 'Supplier tidak valid',
            'quantity.required' => 'Jumlah wajib diisi',
            'quantity.integer' => 'Jumlah harus berupa angka bulat',
            'quantity.min' => 'Jumlah minimal 1',
            'reason.required' => 'Alasan wajib dipilih',
            'reason.in' => 'Alasan tidak valid',
            'description.required' => 'Deskripsi wajib diisi',
            'description.max' => 'Deskripsi maksimal 1000 karakter',
            'return_date.required' => 'Tanggal retur wajib diisi',
            'return_date.date' => 'Format tanggal tidak valid',
            'return_date.before_or_equal' => 'Tanggal retur tidak boleh di masa depan',
        ]);
        
        // Check if store has enough stock
        $storeStock = StoreStock::where('store_id', $user->store_id)
            ->where('product_id', $validatedData['product_id'])
            ->first();
        
        if (!$storeStock || $storeStock->quantity < $validatedData['quantity']) {
            return back()->withErrors(['quantity' => 'Stok tidak mencukupi untuk jumlah retur yang diminta'])
                ->withInput();
        }
        
        // Determine which warehouse should handle this return
        // Get the most recent distribution to this store to determine the warehouse
        $recentDistribution = \App\Models\Distribution::where('store_id', $user->store_id)
            ->whereNotNull('warehouse_id')
            ->orderBy('date_distributed', 'desc')
            ->first();

        $warehouseId = $recentDistribution ? $recentDistribution->warehouse_id : null;

        // If no recent distribution found, assign to the first available warehouse
        if (!$warehouseId) {
            $defaultWarehouse = \App\Models\Warehouse::first();
            $warehouseId = $defaultWarehouse ? $defaultWarehouse->id : null;
        }

        $validatedData['store_id'] = $user->store_id;
        $validatedData['warehouse_id'] = $warehouseId; // Assign to appropriate warehouse
        $validatedData['requested_by'] = $user->id;
        $validatedData['status'] = 'requested';

        $return = ReturnModel::create($validatedData);
        
        return redirect()->route('user.returns.index')
            ->with('success', 'Permintaan retur berhasil dibuat dan menunggu persetujuan admin');
    }

    /**
     * Store a return based on distribution shortage.
     */
    public function storeFromDistribution(Request $request)
    {
        $user = auth()->user();

        // Only allow store users
        if (!$user->isUser() || !$user->store_id) {
            abort(403, 'Akses ditolak');
        }

        $validatedData = $request->validate([
            'distribution_id' => 'required|exists:distributions,id',
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:1',
            'reason' => 'required|in:damaged,expired,defective,overstock,shortage,other',
            'description' => 'required|string|max:1000',
            'return_date' => 'required|date|before_or_equal:today',
        ], [
            'distribution_id.required' => 'Distribusi wajib dipilih',
            'distribution_id.exists' => 'Distribusi tidak valid',
            'product_id.required' => 'Produk wajib dipilih',
            'product_id.exists' => 'Produk tidak valid',
            'quantity.required' => 'Jumlah wajib diisi',
            'quantity.integer' => 'Jumlah harus berupa angka bulat',
            'quantity.min' => 'Jumlah minimal 1',
            'reason.required' => 'Alasan wajib dipilih',
            'reason.in' => 'Alasan tidak valid',
            'description.required' => 'Deskripsi wajib diisi',
            'description.max' => 'Deskripsi maksimal 1000 karakter',
            'return_date.required' => 'Tanggal retur wajib diisi',
            'return_date.date' => 'Format tanggal tidak valid',
            'return_date.before_or_equal' => 'Tanggal retur tidak boleh di masa depan',
        ]);

        // Verify the distribution belongs to the user's store and is confirmed
        $distribution = \App\Models\Distribution::where('id', $validatedData['distribution_id'])
            ->where('store_id', $user->store_id)
            ->where('confirmed', true)
            ->first();

        if (!$distribution) {
            return back()->withErrors(['distribution_id' => 'Distribusi tidak ditemukan atau belum dikonfirmasi'])
                ->withInput();
        }

        // Calculate maximum returnable quantity (simplified system: full quantity minus approved returns)
        $approvedReturns = \App\Models\ReturnModel::where('distribution_id', $distribution->id)
            ->where('status', 'approved')
            ->sum('quantity');

        $maxReturnableQuantity = $distribution->quantity - $approvedReturns;

        if ($maxReturnableQuantity <= 0) {
            return back()->withErrors(['quantity' => 'Tidak ada jumlah yang dapat diretur untuk distribusi ini'])
                ->withInput();
        }

        if ($validatedData['quantity'] > $maxReturnableQuantity) {
            return back()->withErrors(['quantity' => "Jumlah retur tidak boleh melebihi {$maxReturnableQuantity} unit yang tersedia"])
                ->withInput();
        }

        // Verify product matches distribution
        if ($distribution->product_id !== $validatedData['product_id']) {
            return back()->withErrors(['product_id' => 'Produk tidak sesuai dengan distribusi'])
                ->withInput();
        }

        $validatedData['store_id'] = $user->store_id;
        $validatedData['warehouse_id'] = $distribution->warehouse_id; // Use warehouse from distribution
        $validatedData['requested_by'] = $user->id;
        $validatedData['status'] = 'requested';

        $return = ReturnModel::create($validatedData);

        return redirect()->route('user.deliveries')
            ->with('success', 'Permintaan retur berhasil dibuat dan menunggu persetujuan admin');
    }

    /**
     * Display the specified return.
     */
    public function show(ReturnModel $return)
    {
        $user = auth()->user();
        
        // Only allow store users to view their own store's returns
        if (!$user->isUser() || $return->store_id !== $user->store_id) {
            abort(403, 'Akses ditolak');
        }
        
        $return->load(['product', 'supplier', 'requestedBy', 'approvedBy']);
        
        return view('user.returns.show', compact('return'));
    }

    /**
     * Get available months for filter dropdown.
     */
    private function getAvailableMonths($storeId)
    {
        $months = [];

        // Add "All Time" option
        $months[] = [
            'value' => 'all',
            'label' => 'Semua Waktu'
        ];

        // Get months from existing returns for this store
        $returnMonths = ReturnModel::where('store_id', $storeId)
            ->selectRaw('DATE_FORMAT(return_date, "%Y-%m") as month')
            ->distinct()
            ->orderBy('month', 'desc')
            ->pluck('month');

        foreach ($returnMonths as $month) {
            $date = Carbon::createFromFormat('Y-m', $month);
            $months[] = [
                'value' => $month,
                'label' => $date->format('F Y')
            ];
        }

        return $months;
    }
}
