<?php

namespace App\Traits;

use App\Models\Supplier;

trait SupplierHelper
{
    /**
     * Get the current supplier for the authenticated supplier admin user.
     *
     * @return \App\Models\Supplier
     */
    protected function getCurrentSupplier()
    {
        $user = auth()->user();

        // Use the user's supplier_id relationship to get the supplier
        if ($user->supplier_id) {
            return $user->supplier;
        }

        // Fallback: Use the user's actual name directly as the supplier name (legacy support)
        $supplier = Supplier::where('name', $user->name)->first();

        if (!$supplier) {
            // If supplier doesn't exist, create it based on the user's information
            $supplier = Supplier::create([
                'name' => $user->name,
                'contact_person' => $user->name,
                'email' => $user->email,
                'status' => 'active'
            ]);

            // Update the user to link to this supplier
            $user->update(['supplier_id' => $supplier->id]);
        }

        return $supplier;
    }
}
