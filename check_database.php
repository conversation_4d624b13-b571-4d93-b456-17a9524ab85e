<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Checking supplier accounts and their products:\n";
$suppliers = App\Models\Supplier::with('users', 'products')->get();
foreach ($suppliers as $supplier) {
    echo "Supplier: {$supplier->name} (ID: {$supplier->id})\n";
    echo "  Users: " . $supplier->users->pluck('email')->implode(', ') . "\n";
    echo "  Products: " . $supplier->products->pluck('name')->implode(', ') . "\n";
    echo "\n";
}

echo "Checking supplier admin users:\n";
$supplierAdmins = App\Models\User::where('role', 'supplier_admin')->with('supplier')->get();
foreach ($supplierAdmins as $user) {
    echo "User: {$user->email} (supplier_id: {$user->supplier_id})\n";
    if ($user->supplier) {
        echo "  Supplier: {$user->supplier->name}\n";
    } else {
        echo "  No supplier assigned!\n";
    }
    echo "\n";
}

echo "Checking all products:\n";
$products = App\Models\Product::with('supplier')->get();
foreach ($products as $product) {
    echo "Product: {$product->name} (supplier_id: {$product->supplier_id})\n";
    if ($product->supplier) {
        echo "  Supplier: {$product->supplier->name}\n";
    } else {
        echo "  No supplier assigned!\n";
    }
}

echo "\nChecking admin users and warehouses:\n";
$admins = App\Models\User::where('role', 'admin')->with('warehouse')->get();
foreach ($admins as $admin) {
    echo "Admin: {$admin->email} (warehouse_id: {$admin->warehouse_id})\n";
    if ($admin->warehouse) {
        echo "  Warehouse: {$admin->warehouse->name}\n";
    } else {
        echo "  No warehouse assigned!\n";
    }
    echo "\n";
}

echo "\nChecking distributions:\n";
$distributions = App\Models\Distribution::with('product', 'store', 'warehouse')->get();
echo "Total distributions: " . $distributions->count() . "\n";
foreach ($distributions as $distribution) {
    echo "Distribution: {$distribution->product->name} to {$distribution->store->name} (warehouse_id: {$distribution->warehouse_id})\n";
}
