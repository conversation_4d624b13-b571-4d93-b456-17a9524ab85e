<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            SupplierSeeder::class, // Create suppliers first
            UserSeeder::class,     // Then create users (which assigns suppliers to users)
            SimplifiedInventorySeeder::class, // Create shared products and sample data
            // SupplierDeliverySeeder::class,
            // CancelledDeliverySeeder::class,
            // ReturnSeeder::class,
        ]);
    }
}
