<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Product;
use App\Models\Store;
use App\Models\WarehouseStock;
use App\Models\Distribution;
use App\Models\StoreStock;
use App\Models\StockOpname;
use App\Models\Warehouse;
use Carbon\Carbon;

class SimplifiedInventorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample products
        $products = [
            ['name' => 'Smartphone Android Premium'],
            ['name' => 'Laptop Gaming High-End'],
            ['name' => 'Headset Wireless Premium'],
            ['name' => 'Power Bank 20000mAh'],
            ['name' => 'Toolkit Professional'],
            ['name' => 'Kabel USB-C Premium'],
            ['name' => 'Mouse Gaming RGB'],
            ['name' => 'Keyboard Mechanical'],
            ['name' => 'Monitor LED 24 inch'],
            ['name' => 'Speaker Bluetooth Portable'],
        ];

        foreach ($products as $productData) {
            Product::firstOrCreate(['name' => $productData['name']]);
        }

        // Get all stores and products for sample data
        $stores = Store::all();
        $products = Product::all();

        if ($stores->count() > 0 && $products->count() > 0) {
            // Get warehouses for proper data isolation
            $warehouses = Warehouse::all();

            // Create sample warehouse stock for each warehouse (data isolation)
            foreach ($warehouses as $warehouse) {
                foreach ($products as $product) {
                    WarehouseStock::firstOrCreate([
                        'product_id' => $product->id,
                        'store_id' => null, // Central warehouse stock doesn't belong to specific store
                        'warehouse_id' => $warehouse->id, // Assign to specific warehouse
                        'quantity' => rand(50, 200),
                        'date_received' => Carbon::now()->subDays(rand(1, 30)),
                    ]);
                }
            }

            // Create sample distributions for each warehouse (data isolation)
            $sampleStores = $stores->where('name', '!=', 'Gudang Pusat')->take(5);
            foreach ($warehouses as $warehouse) {
                foreach ($sampleStores as $store) {
                    $sampleProducts = $products->random(2); // Reduce to 2 products per store per warehouse
                    foreach ($sampleProducts as $product) {
                        $quantity = rand(10, 50);
                        $confirmed = rand(0, 1) == 1;

                        Distribution::firstOrCreate([
                            'product_id' => $product->id,
                            'store_id' => $store->id,
                            'warehouse_id' => $warehouse->id, // Assign to specific warehouse for data isolation
                            'quantity' => $quantity,
                            'received_quantity' => $confirmed ? $quantity - rand(0, 3) : null, // Some variance in received quantity
                            'notes' => $confirmed ? (rand(0, 1) ? 'Barang diterima dalam kondisi baik' : 'Ada sedikit kerusakan pada kemasan') : null,
                            'date_distributed' => Carbon::now()->subDays(rand(1, 15)),
                            'confirmed' => $confirmed,
                            'confirmed_at' => $confirmed ? Carbon::now()->subDays(rand(0, 3)) : null,
                        ]);
                    }
                }
            }

            // Create sample store stock
            foreach ($sampleStores as $store) {
                $sampleProducts = $products->random(4);
                foreach ($sampleProducts as $product) {
                    StoreStock::firstOrCreate([
                        'store_id' => $store->id,
                        'product_id' => $product->id,
                        'quantity' => rand(5, 30),
                    ]);
                }
            }

            // Create sample stock opname
            foreach ($sampleStores->take(3) as $store) {
                $sampleProducts = $products->random(2);
                foreach ($sampleProducts as $product) {
                    $systemQty = rand(10, 50);
                    $physicalQty = $systemQty + rand(-5, 5); // Small variance
                    
                    StockOpname::firstOrCreate([
                        'store_id' => $store->id,
                        'product_id' => $product->id,
                        'system_quantity' => $systemQty,
                        'physical_quantity' => $physicalQty,
                        'date' => Carbon::now()->subDays(rand(1, 7)),
                    ]);
                }
            }
        }

        $this->command->info('Created simplified inventory sample data');
        $this->command->info('Products: ' . Product::count());
        $this->command->info('Warehouse Stock entries: ' . WarehouseStock::count());
        $this->command->info('Distributions: ' . Distribution::count());
        $this->command->info('Store Stock entries: ' . StoreStock::count());
        $this->command->info('Stock Opname entries: ' . StockOpname::count());
    }
}
