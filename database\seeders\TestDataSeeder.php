<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Product;
use App\Models\Distribution;
use App\Models\SupplierDelivery;
use App\Models\Supplier;
use App\Models\User;
use App\Models\Store;
use App\Models\Warehouse;
use App\Models\WarehouseStock;
use Illuminate\Support\Str;

class TestDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get existing data
        $supplier1 = Supplier::where('name', 'PT. Sumber Makmur')->first();
        $supplier2 = Supplier::where('name', 'CV. Berkah Jaya')->first();
        $warehouse1 = Warehouse::where('code', 'WH001')->first();
        $warehouse2 = Warehouse::where('code', 'WH002')->first();
        $store = Store::where('name', 'Toko Jakarta')->first();

        if (!$supplier1 || !$supplier2 || !$warehouse1 || !$warehouse2 || !$store) {
            $this->command->error('Required base data not found. Please run SupplierSeeder and UserSeeder first.');
            return;
        }

        // Create products for supplier 1
        $product1 = Product::firstOrCreate(
            ['name' => 'Beras Premium 5kg', 'supplier_id' => $supplier1->id],
            [
                'id' => Str::uuid(),
                'description' => 'Beras premium kualitas terbaik',
                'unit' => 'kg',
                'price' => 75000,
                'supplier_id' => $supplier1->id,
            ]
        );

        $product2 = Product::firstOrCreate(
            ['name' => 'Minyak Goreng 2L', 'supplier_id' => $supplier1->id],
            [
                'id' => Str::uuid(),
                'description' => 'Minyak goreng berkualitas',
                'unit' => 'liter',
                'price' => 35000,
                'supplier_id' => $supplier1->id,
            ]
        );

        // Create products for supplier 2
        $product3 = Product::firstOrCreate(
            ['name' => 'Gula Pasir 1kg', 'supplier_id' => $supplier2->id],
            [
                'id' => Str::uuid(),
                'description' => 'Gula pasir putih bersih',
                'unit' => 'kg',
                'price' => 15000,
                'supplier_id' => $supplier2->id,
            ]
        );

        $product4 = Product::firstOrCreate(
            ['name' => 'Tepung Terigu 1kg', 'supplier_id' => $supplier2->id],
            [
                'id' => Str::uuid(),
                'description' => 'Tepung terigu protein tinggi',
                'unit' => 'kg',
                'price' => 12000,
                'supplier_id' => $supplier2->id,
            ]
        );

        // Create supplier deliveries
        $delivery1 = SupplierDelivery::firstOrCreate(
            ['supplier_id' => $supplier1->id, 'product_id' => $product1->id],
            [
                'id' => Str::uuid(),
                'supplier_id' => $supplier1->id,
                'product_id' => $product1->id,
                'quantity' => 100,
                'delivery_date' => now()->subDays(5),
                'status' => 'delivered',
                'warehouse_id' => $warehouse1->id,
            ]
        );

        $delivery2 = SupplierDelivery::firstOrCreate(
            ['supplier_id' => $supplier2->id, 'product_id' => $product3->id],
            [
                'id' => Str::uuid(),
                'supplier_id' => $supplier2->id,
                'product_id' => $product3->id,
                'quantity' => 200,
                'delivery_date' => now()->subDays(3),
                'status' => 'delivered',
                'warehouse_id' => $warehouse2->id,
            ]
        );

        // Create warehouse stock
        WarehouseStock::firstOrCreate(
            ['product_id' => $product1->id, 'warehouse_id' => $warehouse1->id, 'store_id' => null],
            [
                'quantity' => 100,
            ]
        );

        WarehouseStock::firstOrCreate(
            ['product_id' => $product3->id, 'warehouse_id' => $warehouse2->id, 'store_id' => null],
            [
                'quantity' => 200,
            ]
        );

        // Create distributions
        Distribution::firstOrCreate(
            ['product_id' => $product1->id, 'store_id' => $store->id, 'warehouse_id' => $warehouse1->id],
            [
                'id' => Str::uuid(),
                'product_id' => $product1->id,
                'store_id' => $store->id,
                'warehouse_id' => $warehouse1->id,
                'quantity' => 50,
                'date_distributed' => now()->subDays(2),
                'status' => 'delivered',
            ]
        );

        Distribution::firstOrCreate(
            ['product_id' => $product3->id, 'store_id' => $store->id, 'warehouse_id' => $warehouse2->id],
            [
                'id' => Str::uuid(),
                'product_id' => $product3->id,
                'store_id' => $store->id,
                'warehouse_id' => $warehouse2->id,
                'quantity' => 100,
                'date_distributed' => now()->subDays(1),
                'status' => 'delivered',
            ]
        );

        $this->command->info('Test data created successfully!');
        $this->command->info('Products created: ' . Product::count());
        $this->command->info('Supplier deliveries created: ' . SupplierDelivery::count());
        $this->command->info('Distributions created: ' . Distribution::count());
    }
}
