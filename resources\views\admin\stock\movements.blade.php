@extends('layouts.admin')

@section('title', 'Riwayat Stok - Indah Berkah Abadi')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl sm:text-3xl font-bold text-gray-900">Riwayat Pergerakan Stok</h1>
                    <p class="text-gray-600 mt-1">{{ $product->name }}</p>
                </div>
                <div class="flex flex-col sm:flex-row gap-3">
                    <a href="{{ route('admin.stock.index') }}" class="admin-dashboard-btn admin-dashboard-btn-secondary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m0 7h18"></path>
                        </svg>
                        Kembali
                    </a>
                    <a href="{{ route('admin.stock.adjust-form', $product) }}" class="admin-dashboard-btn admin-dashboard-btn-primary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        Sesuaikan Stok
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Product Info -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                <div class="text-center">
                    <p class="text-sm text-gray-600">Stok Gudang</p>
                    @php $warehouseStock = $product->getCurrentWarehouseStock(auth()->user()->getWarehouseId()); @endphp
                    <p class="text-2xl font-bold text-gray-900">{{ number_format($warehouseStock) }}</p>
                    <p class="text-sm text-gray-500">unit</p>
                </div>
                <div class="text-center">
                    <p class="text-sm text-gray-600">Pending Distribusi</p>
                    <p class="text-2xl font-bold text-orange-600">{{ number_format($product->getPendingDistributionsQuantity()) }}</p>
                    <p class="text-sm text-gray-500">unit</p>
                </div>
                <div class="text-center">
                    <p class="text-sm text-gray-600">Stok Tersedia</p>
                    @php $availableStock = $warehouseStock; @endphp
                    <p class="text-2xl font-bold {{ $availableStock >= 0 ? 'text-green-600' : 'text-red-600' }}">
                        {{ number_format($availableStock) }}
                    </p>
                    <p class="text-sm text-gray-500">unit</p>
                    @if($availableStock < 0)
                        <p class="text-xs text-red-500 mt-1">Stok tidak mencukupi!</p>
                    @endif
                </div>
                <div class="text-center">
                    <p class="text-sm text-gray-600">Stok Toko</p>
                    @php
                        $storeStock = $product->storeStock()
                            ->whereHas('store.distributions', function($q) {
                                $q->where('warehouse_id', auth()->user()->getWarehouseId())
                                  ->where('product_id', $product->id);
                            })
                            ->sum('quantity');
                    @endphp
                    <p class="text-2xl font-bold text-blue-600">{{ number_format($storeStock) }}</p>
                    <p class="text-sm text-gray-500">unit</p>
                </div>
                <div class="text-center">
                    <p class="text-sm text-gray-600">Total Distribusi</p>
                    <p class="text-2xl font-bold text-purple-600">{{ number_format($product->distributions->count()) }}</p>
                    <p class="text-sm text-gray-500">kali</p>
                </div>
            </div>

            @if($product->getPendingDistributionsQuantity() > 0)
            <div class="mt-4 p-3 bg-orange-50 border border-orange-200 rounded-lg">
                <div class="flex items-start">
                    <svg class="w-5 h-5 text-orange-600 mt-0.5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                    <div>
                        <p class="text-sm font-medium text-orange-800">Perhatian: Ada Distribusi Pending</p>
                        <p class="text-sm text-orange-700 mt-1">
                            Terdapat {{ number_format($product->getPendingDistributionsQuantity()) }} unit yang sudah dialokasikan untuk distribusi yang belum dikonfirmasi.
                            Stok yang tersedia untuk operasi baru adalah {{ number_format($product->getAvailableStock()) }} unit.
                        </p>
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>

    <!-- Stock Movements Table -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-header">
            <h2 class="admin-dashboard-card-title">Riwayat Pergerakan Stok</h2>
        </div>
        <div class="admin-dashboard-card-content">
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                        <tr>
                            <th class="px-6 py-3">Tanggal</th>
                            <th class="px-6 py-3">Jenis Pergerakan</th>
                            <th class="px-6 py-3">Sumber</th>
                            <th class="px-6 py-3">Jumlah</th>
                            <th class="px-6 py-3">Stok Sebelum</th>
                            <th class="px-6 py-3">Stok Sesudah</th>
                            <th class="px-6 py-3">Catatan</th>
                            <th class="px-6 py-3">Dibuat Oleh</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($movements as $movement)
                        <tr class="bg-white border-b hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">
                                    {{ $movement->created_at->format('d/m/Y H:i') }}
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <span class="px-2 py-1 text-xs font-medium rounded-full
                                    {{ $movement->type === 'in' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                    {{ $movement->type === 'in' ? 'Masuk' : 'Keluar' }}
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">
                                    @switch($movement->source)
                                        @case('adjustment')
                                            Penyesuaian Stok
                                            @break
                                        @case('distribution')
                                            Distribusi
                                            @break
                                        @case('return')
                                            Retur
                                            @break
                                        @case('initial')
                                            Stok Awal
                                            @break
                                        @default
                                            {{ ucfirst($movement->source) }}
                                    @endswitch
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <span class="font-medium {{ $movement->type === 'in' ? 'text-green-600' : 'text-red-600' }}">
                                    {{ $movement->type === 'in' ? '+' : '-' }}{{ number_format($movement->quantity) }} unit
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <span class="font-medium text-gray-900">
                                    {{ number_format($movement->previous_stock) }} unit
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <span class="font-medium text-gray-900">
                                    {{ number_format($movement->new_stock) }} unit
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-600 max-w-xs truncate" title="{{ $movement->notes }}">
                                    {{ $movement->notes ?: '-' }}
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-600">
                                    {{ $movement->creator->name ?? 'Sistem' }}
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="8" class="px-6 py-4 text-center text-gray-500">
                                Belum ada pergerakan stok untuk produk ini
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if($movements->hasPages())
            <div class="mt-4">
                {{ $movements->links('pagination.admin-dashboard') }}
            </div>
            @endif
        </div>
    </div>
</div>
@endsection
