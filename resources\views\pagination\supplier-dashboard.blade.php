@if ($paginator->hasPages())
<div class="supplier-dashboard-pagination">
    <!-- Results Information -->
    <div class="supplier-dashboard-pagination-info">
        <span class="supplier-dashboard-pagination-text">
            @if($paginator->total() > 0)
                Menampilkan {{ $paginator->firstItem() }} - {{ $paginator->lastItem() }} dari {{ $paginator->total() }} hasil
            @else
                Tidak ada hasil ditemukan
            @endif
        </span>
    </div>

    <!-- Pagination Navigation -->
    <div class="supplier-dashboard-pagination-nav">
        {{-- First Page Link --}}
        @if ($paginator->currentPage() > 3)
            <a href="{{ $paginator->url(1) }}" class="supplier-dashboard-pagination-btn supplier-dashboard-pagination-btn-secondary">
                <svg class="supplier-dashboard-pagination-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 19l-7-7 7-7m8 14l-7-7 7-7"></path>
                </svg>
                <span class="supplier-dashboard-pagination-text-desktop">Pertama</span>
            </a>
        @endif

        {{-- Previous Page Link --}}
        @if ($paginator->onFirstPage())
            <span class="supplier-dashboard-pagination-btn supplier-dashboard-pagination-btn-disabled">
                <svg class="supplier-dashboard-pagination-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
                <span class="supplier-dashboard-pagination-text-desktop">Sebelumnya</span>
            </span>
        @else
            <a href="{{ $paginator->previousPageUrl() }}" class="supplier-dashboard-pagination-btn supplier-dashboard-pagination-btn-secondary">
                <svg class="supplier-dashboard-pagination-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
                <span class="supplier-dashboard-pagination-text-desktop">Sebelumnya</span>
            </a>
        @endif

        {{-- Pagination Elements --}}
        @foreach ($elements as $element)
            {{-- "Three Dots" Separator --}}
            @if (is_string($element))
                <span class="supplier-dashboard-pagination-dots">{{ $element }}</span>
            @endif

            {{-- Array Of Links --}}
            @if (is_array($element))
                @foreach ($element as $page => $url)
                    @if ($page == $paginator->currentPage())
                        <span class="supplier-dashboard-pagination-btn supplier-dashboard-pagination-btn-active">{{ $page }}</span>
                    @else
                        <a href="{{ $url }}" class="supplier-dashboard-pagination-btn supplier-dashboard-pagination-btn-secondary">{{ $page }}</a>
                    @endif
                @endforeach
            @endif
        @endforeach

        {{-- Next Page Link --}}
        @if ($paginator->hasMorePages())
            <a href="{{ $paginator->nextPageUrl() }}" class="supplier-dashboard-pagination-btn supplier-dashboard-pagination-btn-secondary">
                <span class="supplier-dashboard-pagination-text-desktop">Selanjutnya</span>
                <svg class="supplier-dashboard-pagination-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </a>
        @else
            <span class="supplier-dashboard-pagination-btn supplier-dashboard-pagination-btn-disabled">
                <span class="supplier-dashboard-pagination-text-desktop">Selanjutnya</span>
                <svg class="supplier-dashboard-pagination-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </span>
        @endif

        {{-- Last Page Link --}}
        @if ($paginator->currentPage() < $paginator->lastPage() - 2)
            <a href="{{ $paginator->url($paginator->lastPage()) }}" class="supplier-dashboard-pagination-btn supplier-dashboard-pagination-btn-secondary">
                <span class="supplier-dashboard-pagination-text-desktop">Terakhir</span>
                <svg class="supplier-dashboard-pagination-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5l7 7-7 7M5 5l7 7-7 7"></path>
                </svg>
            </a>
        @endif
    </div>

</div>
@endif
