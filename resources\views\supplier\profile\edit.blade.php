@extends('layouts.supplier')

@section('title', 'Edit Profil - Indah Be<PERSON>')
@section('page-title', 'Edit Profil')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-content">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl sm:text-3xl font-bold text-gray-900">Edit Profil</h1>
                    <p class="text-gray-600 mt-1">Perbarui informasi profil dan pengaturan akun</p>
                </div>
                <div class="flex flex-col sm:flex-row gap-3">
                    <a href="{{ route('supplier.profile.show') }}" class="supplier-dashboard-btn supplier-dashboard-btn-secondary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Kembali
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="max-w-2xl">
        <div class="supplier-dashboard-card">
            <div class="supplier-dashboard-card-header">
                <h2 class="supplier-dashboard-card-title">Informasi Akun & Perusahaan</h2>
                <p class="text-sm text-gray-600">Perbarui informasi akun dan data perusahaan Anda</p>
            </div>
            <div class="supplier-dashboard-card-content">
                <form method="POST" action="{{ route('supplier.profile.update') }}" class="space-y-8">
                    @csrf
                    @method('PUT')

                    <!-- Account Information Section -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Informasi Akun</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Name -->
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                                    Nama Akun <span class="text-red-500">*</span>
                                </label>
                                <input
                                    id="name"
                                    name="name"
                                    type="text"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('name') border-red-500 @enderror"
                                    placeholder="Masukkan nama akun"
                                    value="{{ old('name', $user->name) }}"
                                    required
                                >
                                @error('name')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Email -->
                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                                    Email Akun <span class="text-red-500">*</span>
                                </label>
                                <input
                                    id="email"
                                    name="email"
                                    type="email"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('email') border-red-500 @enderror"
                                    placeholder="Masukkan alamat email akun"
                                    value="{{ old('email', $user->email) }}"
                                    required
                                >
                                @error('email')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Timezone and Read-only fields -->
                    <div>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <!-- Timezone -->
                            <div>
                                <label for="timezone" class="block text-sm font-medium text-gray-700 mb-2">
                                    Zona Waktu <span class="text-red-500">*</span>
                                </label>
                                <select
                                    id="timezone"
                                    name="timezone"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('timezone') border-red-500 @enderror"
                                    required
                                >
                                    @foreach($timezones as $value => $label)
                                        <option value="{{ $value }}" {{ old('timezone', $user->timezone) === $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('timezone')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Role -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Role</label>
                                <input
                                    type="text"
                                    value="Supplier Administrator"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50"
                                    readonly
                                >
                            </div>

                            <!-- Status -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                                <input
                                    type="text"
                                    value="Aktif"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50"
                                    readonly
                                >
                            </div>
                        </div>
                    </div>

                    <!-- Supplier Company Information Section -->
                    @if($supplier)
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Informasi Perusahaan</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Company Name -->
                            <div>
                                <label for="supplier_name" class="block text-sm font-medium text-gray-700 mb-2">
                                    Nama Perusahaan <span class="text-red-500">*</span>
                                </label>
                                <input
                                    id="supplier_name"
                                    name="supplier_name"
                                    type="text"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('supplier_name') border-red-500 @enderror"
                                    placeholder="Masukkan nama perusahaan"
                                    value="{{ old('supplier_name', $supplier->name) }}"
                                    required
                                >
                                @error('supplier_name')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Contact Person -->
                            <div>
                                <label for="contact_person" class="block text-sm font-medium text-gray-700 mb-2">
                                    Nama Kontak
                                </label>
                                <input
                                    id="contact_person"
                                    name="contact_person"
                                    type="text"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('contact_person') border-red-500 @enderror"
                                    placeholder="Masukkan nama kontak"
                                    value="{{ old('contact_person', $supplier->contact_person) }}"
                                >
                                @error('contact_person')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Phone -->
                            <div>
                                <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                                    Nomor Telepon
                                </label>
                                <input
                                    id="phone"
                                    name="phone"
                                    type="text"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('phone') border-red-500 @enderror"
                                    placeholder="Masukkan nomor telepon"
                                    value="{{ old('phone', $supplier->phone) }}"
                                >
                                @error('phone')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Company Email -->
                            <div>
                                <label for="supplier_email" class="block text-sm font-medium text-gray-700 mb-2">
                                    Email Perusahaan
                                </label>
                                <input
                                    id="supplier_email"
                                    name="supplier_email"
                                    type="email"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('supplier_email') border-red-500 @enderror"
                                    placeholder="Masukkan email perusahaan"
                                    value="{{ old('supplier_email', $supplier->email) }}"
                                >
                                @error('supplier_email')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Address -->
                            <div class="md:col-span-2">
                                <label for="address" class="block text-sm font-medium text-gray-700 mb-2">
                                    Alamat
                                </label>
                                <textarea
                                    id="address"
                                    name="address"
                                    rows="3"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('address') border-red-500 @enderror"
                                    placeholder="Masukkan alamat lengkap perusahaan"
                                >{{ old('address', $supplier->address) }}</textarea>
                                @error('address')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Current timezone info -->
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div class="flex">
                            <svg class="w-5 h-5 text-blue-400 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div>
                                <h4 class="text-sm font-medium text-blue-800">Zona Waktu Saat Ini</h4>
                                <div class="mt-1 text-sm text-blue-700">
                                    <p><strong>{{ $timezones[$user->timezone] ?? $user->timezone }}</strong></p>
                                    <p>Waktu sekarang: <strong>@userNow</strong></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="flex justify-end space-x-3">
                        <a href="{{ route('supplier.profile.show') }}" class="supplier-dashboard-btn supplier-dashboard-btn-secondary">
                            Batal
                        </a>
                        <button type="submit" class="supplier-dashboard-btn supplier-dashboard-btn-primary">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Simpan Perubahan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
