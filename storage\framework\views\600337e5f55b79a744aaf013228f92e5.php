<?php if($paginator->hasPages()): ?>
<div class="supplier-dashboard-pagination">
    <!-- Results Information -->
    <div class="supplier-dashboard-pagination-info">
        <span class="supplier-dashboard-pagination-text">
            <?php if($paginator->total() > 0): ?>
                Menampilkan <?php echo e($paginator->firstItem()); ?> - <?php echo e($paginator->lastItem()); ?> dari <?php echo e($paginator->total()); ?> hasil
            <?php else: ?>
                Tidak ada hasil ditemukan
            <?php endif; ?>
        </span>
    </div>

    <!-- Pagination Navigation -->
    <div class="supplier-dashboard-pagination-nav">
        
        <?php if($paginator->currentPage() > 3): ?>
            <a href="<?php echo e($paginator->url(1)); ?>" class="supplier-dashboard-pagination-btn supplier-dashboard-pagination-btn-secondary">
                <svg class="supplier-dashboard-pagination-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 19l-7-7 7-7m8 14l-7-7 7-7"></path>
                </svg>
                <span class="supplier-dashboard-pagination-text-desktop">Pertama</span>
            </a>
        <?php endif; ?>

        
        <?php if($paginator->onFirstPage()): ?>
            <span class="supplier-dashboard-pagination-btn supplier-dashboard-pagination-btn-disabled">
                <svg class="supplier-dashboard-pagination-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
                <span class="supplier-dashboard-pagination-text-desktop">Sebelumnya</span>
            </span>
        <?php else: ?>
            <a href="<?php echo e($paginator->previousPageUrl()); ?>" class="supplier-dashboard-pagination-btn supplier-dashboard-pagination-btn-secondary">
                <svg class="supplier-dashboard-pagination-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
                <span class="supplier-dashboard-pagination-text-desktop">Sebelumnya</span>
            </a>
        <?php endif; ?>

        
        <?php $__currentLoopData = $elements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $element): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            
            <?php if(is_string($element)): ?>
                <span class="supplier-dashboard-pagination-dots"><?php echo e($element); ?></span>
            <?php endif; ?>

            
            <?php if(is_array($element)): ?>
                <?php $__currentLoopData = $element; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $page => $url): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if($page == $paginator->currentPage()): ?>
                        <span class="supplier-dashboard-pagination-btn supplier-dashboard-pagination-btn-active"><?php echo e($page); ?></span>
                    <?php else: ?>
                        <a href="<?php echo e($url); ?>" class="supplier-dashboard-pagination-btn supplier-dashboard-pagination-btn-secondary"><?php echo e($page); ?></a>
                    <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

        
        <?php if($paginator->hasMorePages()): ?>
            <a href="<?php echo e($paginator->nextPageUrl()); ?>" class="supplier-dashboard-pagination-btn supplier-dashboard-pagination-btn-secondary">
                <span class="supplier-dashboard-pagination-text-desktop">Selanjutnya</span>
                <svg class="supplier-dashboard-pagination-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </a>
        <?php else: ?>
            <span class="supplier-dashboard-pagination-btn supplier-dashboard-pagination-btn-disabled">
                <span class="supplier-dashboard-pagination-text-desktop">Selanjutnya</span>
                <svg class="supplier-dashboard-pagination-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </span>
        <?php endif; ?>

        
        <?php if($paginator->currentPage() < $paginator->lastPage() - 2): ?>
            <a href="<?php echo e($paginator->url($paginator->lastPage())); ?>" class="supplier-dashboard-pagination-btn supplier-dashboard-pagination-btn-secondary">
                <span class="supplier-dashboard-pagination-text-desktop">Terakhir</span>
                <svg class="supplier-dashboard-pagination-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5l7 7-7 7M5 5l7 7-7 7"></path>
                </svg>
            </a>
        <?php endif; ?>
    </div>

</div>
<?php endif; ?>
<?php /**PATH D:\00. RENATA\CODE\indahberkahabadi\resources\views/pagination/supplier-dashboard.blade.php ENDPATH**/ ?>