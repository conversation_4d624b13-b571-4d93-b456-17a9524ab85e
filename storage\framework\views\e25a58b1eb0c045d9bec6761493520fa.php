<?php $__env->startSection('title', 'Inventori Toko - Indah Berkah Abadi'); ?>
<?php $__env->startSection('page-title', 'Inventori Toko'); ?>

<?php $__env->startPush('styles'); ?>
<link rel="stylesheet" href="<?php echo e(asset('css/admin-dashboard-store-inventory.css')); ?>?v=<?php echo e(time()); ?>">
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Header -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl sm:text-3xl font-bold text-gray-900">Inventori Toko</h1>
                    <p class="text-gray-600 mt-1">Kelola stok produk di setiap toko</p>
                </div>
                <?php if($selectedStore): ?>
                <div class="flex flex-col sm:flex-row gap-3">
                    <a href="<?php echo e(route('admin.distributions.create', ['store_id' => $selectedStore->id])); ?>" class="admin-dashboard-btn admin-dashboard-btn-primary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                        </svg>
                        Buat Distribusi
                    </a>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Enhanced Store Selection -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-header">
            <div class="flex items-center gap-3">
                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                </div>
                <div>
                    <h2 class="admin-dashboard-card-title">Pilih Toko</h2>
                    <p class="text-sm text-gray-600 mt-1">Pilih toko untuk melihat inventori produk</p>
                </div>
            </div>
        </div>
        <div class="admin-dashboard-card-content">
            <form method="GET" action="<?php echo e(route('admin.store-inventory.index')); ?>" class="space-y-4">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <div class="lg:col-span-2">
                        <label class="block text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                            <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                            Toko
                        </label>
                        <div class="relative">
                            <select name="store_id" id="store_id"
                                    class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-900 font-medium transition-all duration-200"
                                    onchange="this.form.submit()">
                                <option value="">🏪 Pilih Toko untuk Melihat Inventori</option>
                                <?php $__currentLoopData = $stores; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $store): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($store['id']); ?>" <?php echo e(request('store_id') == $store['id'] ? 'selected' : ''); ?>>
                                        🏢 <?php echo e($store['name']); ?> - <?php echo e($store['location']); ?> (👤 <?php echo e($store['user_name']); ?>)
                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l4-4 4 4m0 6l-4 4-4-4"></path>
                                </svg>
                            </div>
                        </div>
                        <?php if(count($stores) > 0): ?>
                            <p class="mt-2 text-sm text-gray-600 flex items-center gap-1">
                                <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <?php echo e(count($stores)); ?> toko tersedia
                            </p>
                        <?php endif; ?>
                    </div>
                    <?php if($selectedStore): ?>
                        <div>
                            <label class="block text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                                <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                                Cari Produk
                            </label>
                            <div class="flex gap-3">
                                <div class="relative flex-1">
                                    <input type="search" name="search" value="<?php echo e(request('search')); ?>"
                                           placeholder="Cari nama produk..."
                                           class="w-full px-4 py-3 pl-10 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-900 transition-all duration-200">
                                    <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <button type="submit" class="admin-dashboard-btn admin-dashboard-btn-primary px-6">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                    Cari
                                </button>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </form>
        </div>
    </div>

    <?php if($selectedStore): ?>
        <!-- Enhanced Store Statistics -->
        <div class="admin-dashboard-card">
            <div class="admin-dashboard-card-header">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <div>
                        <h2 class="admin-dashboard-card-title">Statistik Inventori</h2>
                        <p class="text-sm text-gray-600 mt-1">Ringkasan data inventori toko</p>
                    </div>
                </div>
            </div>
            <div class="admin-dashboard-card-content">
                <div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
                    <!-- Total Products Card -->
                    <div class="admin-dashboard-stat-card">
                        <div class="admin-dashboard-stat-icon blue">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                        </div>
                        <div class="admin-dashboard-stat-value"><?php echo e($storeStats['total_products'] ?? 0); ?></div>
                        <div class="admin-dashboard-stat-label">Total Produk</div>
                    </div>

                    <!-- Total Stock Card -->
                    <div class="admin-dashboard-stat-card">
                        <div class="admin-dashboard-stat-icon green">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"></path>
                            </svg>
                        </div>
                        <div class="admin-dashboard-stat-value"><?php echo e(number_format($storeStats['total_stock'] ?? 0)); ?></div>
                        <div class="admin-dashboard-stat-label">Total Stok</div>
                    </div>

                    <!-- Pending Distributions Card -->
                    <div class="admin-dashboard-stat-card">
                        <div class="admin-dashboard-stat-icon orange">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="admin-dashboard-stat-value"><?php echo e($storeStats['pending_distributions'] ?? 0); ?></div>
                        <div class="admin-dashboard-stat-label">Distribusi Pending</div>
                    </div>

                    <!-- Total Distributions Card -->
                    <div class="admin-dashboard-stat-card">
                        <div class="admin-dashboard-stat-icon purple">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                            </svg>
                        </div>
                        <div class="admin-dashboard-stat-value"><?php echo e($storeStats['total_distributions'] ?? 0); ?></div>
                        <div class="admin-dashboard-stat-label">Total Distribusi</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Store Information -->
        <div class="admin-dashboard-card">
            <div class="admin-dashboard-card-header">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <h2 class="admin-dashboard-card-title">Informasi Toko</h2>
                        <p class="text-sm text-gray-600 mt-1">Detail lengkap toko yang dipilih</p>
                    </div>
                </div>
            </div>
            <div class="admin-dashboard-card-content">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Nama Toko</label>
                        <div class="text-lg font-semibold text-gray-900"><?php echo e($selectedStore->name); ?></div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Lokasi</label>
                        <div class="text-lg font-semibold text-gray-900"><?php echo e($selectedStore->location); ?></div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Pengelola</label>
                        <div class="text-lg font-semibold text-gray-900"><?php echo e($selectedStore->users->first()->name ?? 'Tidak ada'); ?></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Products Table -->
        <div class="admin-dashboard-card">
            <div class="admin-dashboard-card-header">
                <h2 class="admin-dashboard-card-title">Inventori Produk</h2>
            </div>
            <div class="admin-dashboard-card-content">
                <?php if($storeProducts->count() > 0): ?>
                    <div class="overflow-x-auto">
                        <table class="w-full text-sm text-left">
                            <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3">Produk</th>
                                    <th class="px-6 py-3">Stok Saat Ini</th>
                                    <th class="px-6 py-3">Status</th>
                                    <th class="px-6 py-3">Total Distribusi</th>
                                    <th class="px-6 py-3">Total Diterima</th>
                                    <th class="px-6 py-3">Distribusi Terakhir</th>
                                    <th class="px-6 py-3">Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $storeProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr class="bg-white border-b hover:bg-gray-50">
                                        <td class="px-6 py-4">
                                            <div class="font-medium text-gray-900"><?php echo e($product['name']); ?></div>
                                        </td>
                                        <td class="px-6 py-4">
                                            <span class="font-medium text-gray-900"><?php echo e(number_format($product['current_stock'])); ?></span>
                                            <span class="text-sm text-gray-500">unit</span>
                                        </td>
                                        <td class="px-6 py-4">
                                            <span class="px-2 py-1 text-xs font-medium rounded-full <?php echo e($product['stock_status']['class']); ?>">
                                                <?php echo e($product['stock_status']['label']); ?>

                                            </span>
                                        </td>
                                        <td class="px-6 py-4">
                                            <span class="font-medium text-gray-900"><?php echo e(number_format($product['total_distributed'])); ?></span>
                                            <span class="text-sm text-gray-500">unit</span>
                                        </td>
                                        <td class="px-6 py-4">
                                            <span class="font-medium text-gray-900"><?php echo e(number_format($product['total_received'])); ?></span>
                                            <span class="text-sm text-gray-500">unit</span>
                                        </td>
                                        <td class="px-6 py-4">
                                            <?php if($product['last_distribution']): ?>
                                                <div class="text-sm">
                                                    <div class="font-medium text-gray-900"><?php echo e($product['last_distribution']->date_distributed->format('d/m/Y')); ?></div>
                                                    <div class="text-gray-500"><?php echo e(number_format($product['last_distribution']->quantity)); ?> unit</div>
                                                </div>
                                            <?php else: ?>
                                                <span class="text-gray-400">Belum ada</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4">
                                            <div class="flex items-center gap-2">
                                                <!-- <button type="button"
                                                        class="text-blue-600 hover:text-blue-800 text-sm font-medium"
                                                        onclick="openStockAdjustModal('<?php echo e($selectedStore->id); ?>', '<?php echo e($product['id']); ?>', '<?php echo e(addslashes($product['name'])); ?>', <?php echo e($product['current_stock']); ?>)">
                                                    Sesuaikan
                                                </button> -->
                                                <button type="button"
                                                        class="text-green-600 hover:text-green-800 text-sm font-medium"
                                                        onclick="viewDistributionHistory('<?php echo e($selectedStore->id); ?>', '<?php echo e($product['id']); ?>', '<?php echo e(addslashes($product['name'])); ?>')">
                                                    Riwayat
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="py-8 text-center">
                        <div class="text-gray-500">
                            <svg class="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                            <p class="text-lg font-medium">Belum Ada Produk</p>
                            <p class="text-sm">Toko ini belum memiliki produk yang didistribusikan.</p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php else: ?>
        <!-- No Store Selected -->
        <div class="admin-dashboard-card">
            <div class="admin-dashboard-card-content">
                <div class="py-8 text-center">
                    <div class="text-gray-500">
                        <svg class="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                        <p class="text-lg font-medium">Pilih Toko</p>
                        <p class="text-sm">Silakan pilih toko dari dropdown di atas untuk melihat inventori produk.</p>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Enhanced Stock Adjustment Modal -->
<div id="stockAdjustModal" class="admin-dashboard-modal">
    <div class="admin-dashboard-modal-content">
        <div class="admin-dashboard-modal-header">
            <div class="flex items-center gap-3">
                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2V7a2 2 0 00-2-2h-5m-1.414-1.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="admin-dashboard-modal-title">Sesuaikan Stok Produk</h3>
                    <p class="text-sm text-gray-600 mt-1">Kelola stok produk di toko yang dipilih</p>
                </div>
            </div>
            <button type="button" class="admin-dashboard-modal-close" onclick="closeStockAdjustModal()">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <div class="admin-dashboard-modal-body">
            <form id="stockAdjustForm">
                <?php echo csrf_field(); ?>
                <input type="hidden" id="adjust_store_id" name="store_id">
                <input type="hidden" id="adjust_product_id" name="product_id">

                <!-- Product Information Section -->
                <div class="bg-gray-50 rounded-lg p-4 mb-6">
                    <h4 class="text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                        <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                        Informasi Produk
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="admin-dashboard-form-group mb-0">
                            <label class="admin-dashboard-label">Nama Produk</label>
                            <div id="adjust_product_name" class="admin-dashboard-info-display"></div>
                        </div>
                        <div class="admin-dashboard-form-group mb-0">
                            <label class="admin-dashboard-label">Stok Saat Ini</label>
                            <div id="adjust_current_stock" class="admin-dashboard-info-display"></div>
                        </div>
                    </div>
                </div>

                <!-- Adjustment Details Section -->
                <div class="space-y-4">
                    <div class="admin-dashboard-form-group">
                        <label for="adjustment_type" class="admin-dashboard-label">
                            <span class="flex items-center gap-2">
                                <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                                </svg>
                                Jenis Penyesuaian
                            </span>
                        </label>
                        <select name="adjustment_type" id="adjustment_type" class="admin-dashboard-select" required>
                            <option value="">-- Pilih Jenis Penyesuaian --</option>
                            <option value="add">➕ Tambah Stok</option>
                            <option value="subtract">➖ Kurangi Stok</option>
                            <option value="set">🎯 Set Stok Baru</option>
                        </select>
                        <div id="adjustment_type_help" class="mt-2 text-xs text-gray-500 hidden"></div>
                    </div>

                    <div class="admin-dashboard-form-group">
                        <label for="quantity" class="admin-dashboard-label">
                            <span class="flex items-center gap-2">
                                <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14"></path>
                                </svg>
                                Jumlah
                            </span>
                        </label>
                        <input type="number" name="quantity" id="quantity" class="admin-dashboard-input" min="0" required placeholder="Masukkan jumlah...">
                        <div id="quantity_preview" class="mt-2 text-sm hidden"></div>
                    </div>

                    <div class="admin-dashboard-form-group">
                        <label for="notes" class="admin-dashboard-label">
                            <span class="flex items-center gap-2">
                                <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2V7a2 2 0 00-2-2h-5m-1.414-1.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                                Catatan (Opsional)
                            </span>
                        </label>
                        <textarea name="notes" id="notes" class="admin-dashboard-textarea" rows="3" placeholder="Alasan penyesuaian stok (contoh: koreksi inventori, barang rusak, dll.)"></textarea>
                    </div>
                </div>
            </form>
        </div>
        <div class="admin-dashboard-modal-footer">
            <button type="button" class="admin-dashboard-btn admin-dashboard-btn-secondary" onclick="closeStockAdjustModal()">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                Batal
            </button>
            <button type="button" class="admin-dashboard-btn admin-dashboard-btn-primary" onclick="submitStockAdjustment()">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                Sesuaikan Stok
            </button>
        </div>
    </div>
</div>

<!-- Enhanced Distribution History Modal -->
<div id="distributionHistoryModal" class="admin-dashboard-modal">
    <div class="admin-dashboard-modal-content admin-dashboard-modal-large">
        <div class="admin-dashboard-modal-header">
            <div class="flex items-center gap-3">
                <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="admin-dashboard-modal-title">Riwayat Distribusi</h3>
                    <p class="text-sm text-gray-600 mt-1">Lihat semua distribusi produk ke toko ini</p>
                </div>
            </div>
            <button type="button" class="admin-dashboard-modal-close" onclick="closeDistributionHistoryModal()">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <div class="admin-dashboard-modal-body">
            <div id="distributionHistoryContent">
                <!-- Content will be loaded via AJAX -->
            </div>
        </div>
        <div class="admin-dashboard-modal-footer">
            <button type="button" class="admin-dashboard-btn admin-dashboard-btn-secondary" onclick="closeDistributionHistoryModal()">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                Tutup
            </button>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="<?php echo e(asset('js/admin-store-inventory.js')); ?>?v=<?php echo e(time()); ?>"></script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\00. RENATA\CODE\indahberkahabadi\resources\views/admin/store-inventory/index.blade.php ENDPATH**/ ?>